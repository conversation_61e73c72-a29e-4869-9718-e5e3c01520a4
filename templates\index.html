<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品数据提取工具</title>
    <style>
        :root {
            --primary-color: #4299E1;
            --success-color: #48BB78;
            --warning-color: #ED8936;
            --light-gray: #F5F7FA;
            --medium-gray: #E2E8F0;
            --dark-gray: #2D3748;
            --text-color: #4A5568;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', 'Source Han Sans CN', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif;
            background-color: var(--light-gray);
            color: var(--dark-gray);
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 400px 1fr;
            grid-template-rows: 1fr;
            height: 100vh;
            gap: 0;
        }

        .left-panel {
            background: white;
            border-right: 1px solid var(--medium-gray);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .right-panel {
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .panel-section {
            padding: 20px;
            border-bottom: 1px solid var(--medium-gray);
        }

        .panel-section:last-child {
            border-bottom: none;
            flex: 1;
            overflow: hidden;
        }

        .scrollable-content {
            overflow-y: auto;
            flex: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .card-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--dark-gray);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 5px;
            flex-shrink: 0;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--dark-gray);
            border-bottom: 1px solid var(--medium-gray);
            padding-bottom: 8px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }

        .form-group input[type="text"] {
            width: 100%;
            max-width: 300px;
            padding: 8px 12px;
            border: 1px solid var(--medium-gray);
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        textarea {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 1px solid var(--medium-gray);
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-success { background-color: var(--success-color); color: white; }
        .btn-warning { background-color: var(--warning-color); color: white; }

        .button-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .button-group .btn {
            width: 100%;
            margin: 0;
        }

        .btn-nav {
            padding: 8px 16px;
            border: 1px solid #D1D5DB;
            border-radius: 4px;
            background-color: white;
            color: #374151;
            cursor: pointer;
        }

        .console {
            background-color: #2D3748;
            color: #F7FAFC;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            padding: 15px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
        }
        .log-time { color: #A0AEC0; margin-right: 10px; }
        .log-success { color: #9AE6B4; }
        .log-warning { color: #F6E05E; }
        .log-error { color: #FEB2B2; }



        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 12px;
            padding: 8px 0;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border-left: 4px solid var(--primary-color);
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .product-card.completed {
            border-left-color: var(--success-color);
            background-color: #F0FFF4;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            border-radius: 8px;
            width: 95%;
            max-width: 1800px;
            height: 95vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid var(--medium-gray);
            background-color: #F8F9FA;
        }
        .modal-header h3 { font-size: 18px; }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #A0AEC0;
            background: none;
            border: none;
        }

        .modal-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding: 15px;
        }

        .comparison-area {
            flex: 1;
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            min-height: 0;
        }

        .comparison-pane {
            width: 50%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #F9FAFB;
            border-radius: 8px;
            border: 1px solid var(--medium-gray);
            position: relative;
            overflow: hidden;
        }

        .comparison-pane img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease-out;
            transform: scale(1);
        }
        
        /* API图片默认放大25% */
        #apiPane img {
            transform: scale(1.25);
        }
        
        #apiPane:hover img {
            transform: scale(1.25);
        }
        
        #apiPane.zoomed img {
            transform: scale(3.125); /* 悬停时在1.25基础上再放大2.5倍 */
        }
        .comparison-pane:hover img {
            cursor: zoom-in;
        }
        .comparison-pane.zoomed img {
            transform: scale(2.5);
            cursor: crosshair;
        }
        .comparison-pane.zoomed .zoom-icon {
            display: none;
        }

        .local-thumbnails-area {
            height: 140px;
            border-top: 1px solid var(--medium-gray);
            padding-top: 15px;
        }

        .local-thumbnails-grid {
            height: 100%;
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 10px;
        }

        .thumbnail-item {
            height: 120px;
            width: 120px;
            flex-shrink: 0;
            border: 3px solid transparent;
            border-radius: 6px;
            cursor: pointer;
            overflow: hidden;
            position: relative;
            background-color: var(--medium-gray);
        }
        .thumbnail-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .thumbnail-item.selected {
            border-color: var(--success-color);
            box-shadow: 0 0 10px rgba(72, 187, 120, 0.5);
        }
        .thumbnail-item:hover {
            border-color: var(--primary-color);
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--medium-gray);
            background-color: #F8F9FA;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }

        .image-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #9CA3AF;
        }
        .image-placeholder-icon { font-size: 48px; margin-bottom: 10px; }
        
        .hidden { display: none; }

        .image-viewer-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.85);
            z-index: 9999;
            justify-content: center;
            align-items: center;
            cursor: zoom-out;
        }
        .image-viewer-overlay img {
            max-width: 90vw;
            max-height: 90vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.5);
        }
        .zoom-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 32px;
            height: 32px;
            background-color: rgba(0,0,0,0.5);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: zoom-in;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 10;
        }
        .comparison-pane:hover .zoom-icon {
            opacity: 1;
        }

        /* 通知系统样式 */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        }

        .notification {
            background: white;
            border-radius: 8px;
            padding: 16px 20px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-left: 4px solid var(--primary-color);
            min-width: 300px;
            max-width: 400px;
            pointer-events: auto;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success {
            border-left-color: var(--success-color);
        }

        .notification.warning {
            border-left-color: var(--warning-color);
        }

        .notification.error {
            border-left-color: #E53E3E;
        }

        .notification-content {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .notification-icon {
            font-size: 20px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .notification-text {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #A0AEC0;
            cursor: pointer;
            padding: 0;
            margin-left: 8px;
            flex-shrink: 0;
        }

        .notification-close:hover {
            color: var(--dark-gray);
        }
    </style>
</head>
<body>
    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container"></div>

    <div class="main-layout">
        <!-- 左侧面板 -->
        <div class="left-panel">
            <!-- 配置选项 -->
            <div class="panel-section">
                <div class="section-title">配置选项</div>
                <div class="form-group">
                    <label>搜索路径：</label>
                    <input type="text" id="searchPath" value="E:\图片\原图">
                </div>
                <div class="form-group">
                    <label>目标路径后缀：</label>
                    <input type="text" id="pathFilter" value="\导出图\已完成">
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="strictSearch" checked>
                    <label for="strictSearch">严格搜索路径限制</label>
                </div>
            </div>

            <!-- JSON输入 -->
            <div class="panel-section">
                <div class="section-title">JSON输入</div>
                <textarea id="jsonInput" placeholder="请在此粘贴JSON数据..." style="height: 150px; resize: vertical;"></textarea>
                <div class="button-group" style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="pasteFromClipboard()">一键粘贴</button>
                    <button class="btn btn-success" onclick="parseAndDownload()">解析并下载</button>
                    <button class="btn btn-warning" onclick="startImageSelection()">开始选择图片</button>
                </div>
            </div>

            <!-- 操作日志 -->
            <div class="panel-section scrollable-content">
                <div class="section-title">操作日志</div>
                <div id="console" class="console" style="height: 100%; margin: 0;"></div>
            </div>
        </div>

        <!-- 右侧面板 -->
        <div class="right-panel">
            <div class="card" style="margin: 20px; flex: 1;">
                <div id="productsSection" class="hidden" style="height: 100%; display: flex; flex-direction: column;">
                    <div class="card-title">商品列表</div>
                    <div id="productsGrid" class="products-grid scrollable-content"></div>
                </div>
                <div id="welcomeSection" style="display: flex; align-items: center; justify-content: center; height: 100%; color: #9CA3AF; text-align: center;">
                    <div>
                        <div style="font-size: 48px; margin-bottom: 20px;">📦</div>
                        <div style="font-size: 18px; margin-bottom: 10px;">商品数据提取工具</div>
                        <div style="font-size: 14px;">请在左侧输入JSON数据开始处理</div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Status Bar -->
    <div class="status-bar"><span id="statusText">就绪</span></div>

    <!-- Image Viewer Overlay -->
    <div id="imageViewer" class="image-viewer-overlay" onclick="hideViewer()"><img id="viewerImg" src=""></div>

    <!-- Image Selection Modal -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">图片对比选择</h3>
                <button class="close" onclick="closeImageModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="comparison-area">
                    <div id="apiPane" class="comparison-pane"></div>
                    <div id="localPane" class="comparison-pane"></div>
                </div>
                <div class="local-thumbnails-area">
                    <div id="localThumbnailsGrid" class="local-thumbnails-grid"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-nav" id="prevBtn" onclick="navigateProduct(-1)">上一个</button>
                <button class="btn-nav" id="nextBtn" onclick="navigateProduct(1)">下一个</button>
                <button class="btn btn-success" onclick="confirmSelection()">确认选择</button>
                <button class="btn" onclick="skipProduct()" style="background-color: #A0AEC0; color: white;">跳过</button>
                <button class="btn" onclick="closeImageModal()" style="background-color: #6B7280; color: white;">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // State variables
        let currentProducts = [];
        let currentJsonData = {};
        let selectedImages = {};
        let currentProductIndex = 0;
        let currentLocalImages = [];
        let currentApiImageUrl = '';
        let currentApiImageUrlRaw = '';
        let selectedImageInfo = null;

        // DOM Elements
        const $ = (selector) => document.querySelector(selector);
        const $$ = (selector) => document.querySelectorAll(selector);

        // Viewer Functions
        function showViewer(src) {
            event.stopPropagation();
            $('#viewerImg').src = src;
            $('#imageViewer').style.display = 'flex';
        }
        function hideViewer() {
            $('#imageViewer').style.display = 'none';
        }

        // 通知系统
        function showNotification(message, type = 'info', duration = 4000) {
            const container = $('#notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            const icons = {
                success: '✅',
                warning: '⚠️',
                error: '❌',
                info: 'ℹ️'
            };

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">${icons[type] || icons.info}</div>
                    <div class="notification-text">${message}</div>
                    <button class="notification-close" onclick="removeNotification(this.parentElement.parentElement)">&times;</button>
                </div>
            `;

            container.appendChild(notification);

            // 触发动画
            setTimeout(() => notification.classList.add('show'), 10);

            // 自动移除
            if (duration > 0) {
                setTimeout(() => removeNotification(notification), duration);
            }

            return notification;
        }

        function removeNotification(notification) {
            if (!notification || !notification.parentElement) return;
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.parentElement.removeChild(notification);
                }
            }, 300);
        }

        // UI Update Functions
        function addLog(message, type = 'info') {
            const consoleEl = $('#console');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<span class="log-time">[${new Date().toLocaleTimeString()}]</span> ${message}`;
            consoleEl.appendChild(logEntry);
            consoleEl.scrollTop = consoleEl.scrollHeight;
        }
        function clearLog() { $('#console').innerHTML = ''; }

        // Core App Logic
        async function pasteFromClipboard() {
            try {
                const response = await fetch('/api/get_clipboard');
                const result = await response.json();
                if (!result.success) throw new Error(result.message);
                $('#jsonInput').value = result.content;
                addLog('✓ 已从剪切板粘贴内容', 'success');
                showNotification('已从剪切板粘贴内容', 'success');
            } catch (err) {
                addLog(`× 获取剪切板内容失败: ${err.message}`, 'error');
                showNotification('获取剪切板内容失败，请手动粘贴', 'warning');
            }
        }

        async function parseAndDownload() {
            const jsonInput = $('#jsonInput').value.trim();
            if (!jsonInput) {
                showNotification('请先粘贴JSON内容', 'warning');
                return;
            }
            clearLog();
            addLog('开始解析JSON数据...', 'info');
            showNotification('正在解析JSON数据...', 'info');
            try {
                const response = await fetch('/api/parse_json', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ json_data: jsonInput })
                });
                const result = await response.json();
                if (result.logs) result.logs.forEach(log => addLog(log.message, log.type));
                if (!result.success) throw new Error(result.message);

                currentProducts = result.products;
                currentJsonData = JSON.parse(jsonInput);
                displayProducts();
                addLog(result.message, 'success');
                showNotification(`解析完成！共处理 ${result.products.length} 个商品`, 'success');
            } catch (error) {
                addLog(`× 解析失败: ${error.message}`, 'error');
                showNotification(`解析失败: ${error.message}`, 'error');
            }
        }

        function displayProducts() {
            const grid = $('#productsGrid');
            grid.innerHTML = '';
            currentProducts.forEach((product, index) => {
                const isCompleted = selectedImages[product.id];
                const card = document.createElement('div');
                card.className = `product-card ${isCompleted ? 'completed' : ''}`;
                card.innerHTML = `
                    <div style="font-size: 14px; line-height: 1.4; color: var(--dark-gray);">${product.name}</div>
                    <div style="font-size: 12px; color: #9CA3AF;">ID: ${product.id}</div>
                    <button class="btn btn-primary" style="font-size: 12px; padding: 8px 12px;" onclick="selectProductImage(${index})">
                        ${isCompleted ? '重新选择' : '选择图片'}
                    </button>
                `;
                grid.appendChild(card);
            });
            $('#productsSection').classList.remove('hidden');
            $('#welcomeSection').style.display = 'none';
        }

        // Image Selection Modal Logic
        function startImageSelection() {
            if (currentProducts.length === 0) {
                showNotification('请先解析JSON数据', 'warning');
                return;
            }
            showNotification('开始图片选择流程', 'info');
            selectProductImage(0);
        }

        async function selectProductImage(index) {
            currentProductIndex = index;
            const product = currentProducts[index];
            addLog(`🔍 正在为第 ${index + 1}/${currentProducts.length} 个商品搜索图片...`, 'info');
            try {
                const response = await fetch('/api/search_images', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        product_name: product.name,
                        product_id: product.id,
                        search_path: $('#searchPath').value,
                        strict_search: $('#strictSearch').checked,
                        json_data: currentJsonData
                    })
                });
                const result = await response.json();
                if (!result.success) throw new Error(result.message);

                currentLocalImages = result.local_images;
                currentApiImageUrl = result.api_image_url;
                currentApiImageUrlRaw = result.api_image_url_raw;
                addLog(`✓ 找到 ${result.local_images.length} 张本地图片`, 'success');
                showImageModal();
            } catch (error) {
                addLog(`× 搜索图片失败: ${error.message}`, 'error');
            }
        }

        function showImageModal() {
            $('#modalTitle').textContent = `图片对比 (${currentProductIndex + 1} / ${currentProducts.length})`;
            $('#prevBtn').disabled = currentProductIndex === 0;
            $('#nextBtn').disabled = currentProductIndex === currentProducts.length - 1;
            
            displayApiImage();
            displayLocalThumbnails();
            
            $('#imageModal').style.display = 'block';
        }

        function displayApiImage() {
            const pane = $('#apiPane');
            if (currentApiImageUrl) {
                const viewerUrl = currentApiImageUrlRaw || currentApiImageUrl;
                pane.innerHTML = `
                    <span class="zoom-icon" onclick="showViewer('${viewerUrl}')">🔍</span>
                    <img src="${currentApiImageUrl}" alt="API图" onerror="this.parentElement.innerHTML = createPlaceholder('📷', '参考图加载失败');">
                `;
            } else {
                pane.innerHTML = createPlaceholder('📷', '无商品参考图');
            }
        }

        function displayLocalThumbnails() {
            const grid = $('#localThumbnailsGrid');
            grid.innerHTML = '';
            selectedImageInfo = null;
            $('#localPane').innerHTML = createPlaceholder('🖼️', '请从下方选择本地图片');

            if (currentLocalImages.length > 0) {
                currentLocalImages.forEach((image, index) => {
                    const thumb = document.createElement('div');
                    thumb.className = 'thumbnail-item';
                    thumb.dataset.index = index;
                    thumb.innerHTML = `<img src="${image.url}" alt="${image.name}" onerror="this.parentElement.innerHTML = createPlaceholder('🖼️', '加载失败');">`;
                    thumb.onclick = () => updateLocalComparisonPane(image, thumb);
                    grid.appendChild(thumb);
                });
                // Auto-select the first one
                grid.firstChild.click();
            } else {
                grid.innerHTML = `<div style="width:100%; text-align:center; color:#9CA3AF;">未找到本地图片</div>`;
            }
        }

        function updateLocalComparisonPane(image, thumbElement) {
            $$('.thumbnail-item').forEach(t => t.classList.remove('selected'));
            thumbElement.classList.add('selected');

            const pane = $('#localPane');
            pane.innerHTML = `
                <span class="zoom-icon" onclick="showViewer('${image.url}')">🔍</span>
                <img src="${image.url}" alt="本地图" onerror="this.parentElement.innerHTML = createPlaceholder('🖼️', '本地图片加载失败');">
            `;
            
            selectedImageInfo = {
                type: 'local',
                url: image.url,
                extension: image.name.substring(image.name.lastIndexOf('.'))
            };
        }

        function createPlaceholder(icon, text) {
            return `<div class="image-placeholder"><div class="image-placeholder-icon">${icon}</div><div>${text}</div></div>`;
        }

        async function navigateProduct(direction) {
            const newIndex = currentProductIndex + direction;
            if (newIndex >= 0 && newIndex < currentProducts.length) {
                await selectProductImage(newIndex);
            }
        }

        async function confirmSelection() {
            if (!selectedImageInfo) {
                showNotification('请先选择一张图片', 'warning');
                return;
            }
            const product = currentProducts[currentProductIndex];
            addLog(`⏳ 正在下载 ${product.id}...`, 'info');
            try {
                const response = await fetch('/api/download_selected', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        product_id: product.id,
                        ...selectedImageInfo
                    })
                });
                const result = await response.json();
                if (!result.success) throw new Error(result.message);

                selectedImages[product.id] = selectedImageInfo;
                addLog(`✓ 图片下载成功: ${result.file_path}`, 'success');
                showNotification(`图片下载成功: ${product.id}`, 'success');
                displayProducts();

                if (currentProductIndex < currentProducts.length - 1) {
                    await navigateProduct(1);
                } else {
                    addLog('🎉 所有商品图片选择完成！', 'success');
                    showNotification('所有商品图片选择完成！', 'success');
                    closeImageModal();
                }
            } catch (error) {
                addLog(`× 下载失败: ${error.message}`, 'error');
                showNotification(`下载失败: ${error.message}`, 'error');
            }
        }

        async function skipProduct() {
            addLog(`⏭️ 跳过商品 ${currentProducts[currentProductIndex].id}`, 'warning');
            if (currentProductIndex < currentProducts.length - 1) {
                await navigateProduct(1);
            } else {
                addLog('已是最后一个商品', 'info');
                closeImageModal();
            }
        }

        function closeImageModal() { $('#imageModal').style.display = 'none'; }

        // Global Event Listeners
        document.addEventListener('keydown', e => {
            if (e.key === "Escape") {
                hideViewer();
                closeImageModal();
            }
        });
        function initializeZoomFeature() {
            const setupPaneZoom = (pane) => {
                pane.addEventListener('mouseenter', () => {
                    const img = pane.querySelector('img');
                    if (img && img.src && img.complete && img.naturalHeight !== 0) {
                        pane.classList.add('zoomed');
                    }
                });

                pane.addEventListener('mouseleave', () => {
                    const img = pane.querySelector('img');
                    pane.classList.remove('zoomed');
                    if (img) {
                        img.style.transformOrigin = 'center center';
                    }
                });

                pane.addEventListener('mousemove', (e) => {
                    const img = pane.querySelector('img');
                    if (!pane.classList.contains('zoomed') || !img) return;

                    const rect = pane.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const xPercent = (x / rect.width) * 100;
                    const yPercent = (y / rect.height) * 100;

                    img.style.transformOrigin = `${xPercent}% ${yPercent}%`;
                });
            };

            setupPaneZoom($('#apiPane'));
            setupPaneZoom($('#localPane'));
        }

        document.addEventListener('DOMContentLoaded', () => {
            addLog('商品数据提取工具已就绪', 'info');
            initializeZoomFeature();
        });
    </script>
</body>
</html>